import { useState, useEffect } from 'react';
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
} from 'chart.js';
import '../../../styles/StatisticsPlaceholder.css';
import userGroupIcon from '../../../assets/users.svg';
import fileIcon from '../../../assets/file-text.svg';
import chartIcon from '../../../assets/chart-column-decreasing.svg';
import tichIcon from '../../../assets/tich.svg';
import dropdownIcon from '../../../assets/icon-sidebar/dropdown.svg';
import legendNodeIcon from '../../../assets/LegendNode.svg';
import searchIcon from '../../../assets/search.svg';
import todayIcon from '../../../assets/today.svg';
import downloadIcon from '../../../assets/download.svg';
import eyeIcon from '../../../assets/eye.svg';

// Đăng ký các components Chart.js cần thiết
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
);

const StatisticsPlaceholder = () => {
  const [selectedDepartment, setSelectedDepartment] = useState('Phòng IT');
  const [selectedYear, setSelectedYear] = useState('2025');
  const [openDepartmentDropdown, setOpenDepartmentDropdown] = useState(false);
  const [openYearDropdown, setOpenYearDropdown] = useState(false);

  // Table filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTableProject, setSelectedTableProject] = useState('Tất cả dự án');
  const [selectedStatus, setSelectedStatus] = useState('Tất cả trạng thái');
  const [selectedDateRange, setSelectedDateRange] = useState({ start: null, end: null });
  const [isTableProjectOpen, setIsTableProjectOpen] = useState(false);
  const [isStatusOpen, setIsStatusOpen] = useState(false);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.statistics-placeholder-dropdown') && !event.target.closest('.statistics-placeholder-year-dropdown')) {
        setOpenDepartmentDropdown(false);
        setOpenYearDropdown(false);
      }
      if (!event.target.closest('.procedure-dropdown')) {
        setIsTableProjectOpen(false);
        setIsStatusOpen(false);
      }
      if (!event.target.closest('.date-picker-container') && !event.target.closest('.filter-date')) {
        setIsDatePickerOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const formatDateRange = () => {
    if (!selectedDateRange.start && !selectedDateRange.end) {
      return 'Chọn mốc thời gian';
    }
    if (selectedDateRange.start && !selectedDateRange.end) {
      return `${selectedDateRange.start.toLocaleDateString('vi-VN')} - ...`;
    }
    if (selectedDateRange.start && selectedDateRange.end) {
      return `${selectedDateRange.start.toLocaleDateString('vi-VN')} - ${selectedDateRange.end.toLocaleDateString('vi-VN')}`;
    }
    return 'Chọn mốc thời gian';
  };

  // Mock data cho thống kê
  const statisticsData = {
    'Phòng IT': {
      employees: 12,
      projects: 50,
      tasks: 24,
      completionRate: 68
    },
    'Phòng Marketing': {
      employees: 8,
      projects: 25,
      tasks: 18,
      completionRate: 75
    },
    'Phòng Kế Toán': {
      employees: 6,
      projects: 15,
      tasks: 12,
      completionRate: 82
    }
  };
  
  // Tạo gradient cho background
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const gradient = ctx.createLinearGradient(0, 0, 0, 400);
  gradient.addColorStop(0, 'rgba(112, 134, 253, 0.3)');
  gradient.addColorStop(1, 'rgba(112, 134, 253, 0.05)');

  // Mock data cho biểu đồ line chart
  const chartData = {
    labels: ['01-2025', '02-2025', '03-2025', '04-2025', '05-2025', '06-2025',
             '07-2025', '08-2025', '09-2025', '10-2025', '11-2025', '12-2025'],
    datasets: [
      {
        label: 'Tỉ lệ hoàn thành',
        data: [70, 18, 32, 20, 41, 11, 57, 15, 56, 52, 91, 42],
        borderColor: '#7086FD',
        backgroundColor: gradient,
        borderWidth: 1,
        fill: true,
        tension: 0,
        pointBackgroundColor: "#7086FD",
        pointBorderColor: "#FFFFFF",
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6, 
        pointHoverBackgroundColor: "#7086FD",
        pointHoverBorderColor: "#FFFFFF",
        pointHoverBorderWidth: 2,
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: '#fff',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: '#e0e0e0',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            return context.parsed.y + '%';
          }
        }
      },
    },
    scales: {
      x: {
        offset: true,
        grid: {
          color: '#e5e5e5',
          drawBorder: false,
          lineWidth: 1,
        },
        ticks: {
          color: '#666',
          font: {
            size: 11,
          },
          maxRotation: 0,
        },
      },
      y: {
        beginAtZero: true,
        max: 100,
        grid: {
          color: '#e5e5e5',
          drawBorder: false,
          lineWidth: 1,
        },
        ticks: {
          color: '#666',
          font: {
            size: 11,
          },
          stepSize: 20,
          callback: function(value) {
            return value;
          },
        },
      },
    },
    elements: {
      point: {
        hoverBackgroundColor: 'rgba(54, 162, 235, 1)',
        hoverBorderColor: 'rgba(54, 162, 235, 1)',
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
  };

  const departments = ['Phòng IT', 'Phòng Marketing', 'Phòng Kế Toán', 'Hành Chính Nhân Sự'];
  const years = ['2023', '2024', '2025'];
  
  // Table options
  const projectOptions = ['Tất cả dự án', 'Dự án A', 'Dự án B', 'Dự án C'];
  const statusOptions = ['Tất cả trạng thái', 'Hoàn thành', 'Đang triển khai', 'Đang chờ', 'Quá hạn'];

  // Mock data cho table với pie chart data
  const employeeData = [
    {
      id: 1,
      name: 'Nguyễn Hoài Gia Bảo',
      position: 'Phòng IT',
      avatar: '/api/placeholder/32/32',
      totalProjects: 4,
      totalTasks: 3,
      completionRate: 35,
      status: 'Hoàn thành',
      statusData: {
        labels: ['Hoàn thành', 'Đang chờ', 'Quá hạn', 'Đang xem xét', 'Đang triển khai'],
        data: [30, 2, 2, 2, 2],
        colors: ['#4ade80', '#e0e0e0', '#f97316', '#8b5cf6', '#06b6d4']
      }
    },
    {
      id: 2,
      name: 'Hồ Tấn Sanh',
      position: 'Phòng IT',
      avatar: '/api/placeholder/32/32',
      totalProjects: 4,
      totalTasks: 3,
      completionRate: 35,
      status: 'Đang triển khai',
      statusData: {
        labels: ['Hoàn thành', 'Đang chờ', 'Quá hạn', 'Đang xem xét', 'Đang triển khai'],
        data: [30, 2, 2, 2, 2],
        colors: ['#4ade80', '#e0e0e0', '#f97316', '#8b5cf6', '#06b6d4']
      }
    },
    {
      id: 3,
      name: 'Cao Văn Nhân',
      position: 'Phòng IT',
      avatar: '/api/placeholder/32/32',
      totalProjects: 4,
      totalTasks: 3,
      completionRate: 35,
      status: 'Đang chờ',
      statusData: {
        labels: ['Hoàn thành', 'Đang chờ', 'Quá hạn', 'Đang xem xét', 'Đang triển khai'],
        data: [30, 2, 2, 2, 2],
        colors: ['#4ade80', '#e0e0e0', '#f97316', '#8b5cf6', '#06b6d4']
      }
    },
    {
      id: 4,
      name: 'Vũ Phan Hoài Nam',
      position: 'Phòng IT',
      avatar: '/api/placeholder/32/32',
      totalProjects: 4,
      totalTasks: 3,
      completionRate: 35,
      status: 'Hoàn thành',
      statusData: {
        labels: ['Hoàn thành', 'Đang chờ', 'Quá hạn', 'Đang xem xét', 'Đang triển khai'],
        data: [30, 2, 2, 2, 2],
        colors: ['#4ade80', '#e0e0e0', '#f97316', '#8b5cf6', '#06b6d4']
      }
    },
    {
      id: 5,
      name: 'Phan Đăng Huynh',
      position: 'Phòng IT',
      avatar: '/api/placeholder/32/32',
      totalProjects: 4,
      totalTasks: 3,
      completionRate: 35,
      status: 'Quá hạn',
      statusData: {
        labels: ['Hoàn thành', 'Đang chờ', 'Quá hạn', 'Đang xem xét', 'Đang triển khai'],
        data: [30, 2, 2, 2, 2],
        colors: ['#4ade80', '#e0e0e0', '#f97316', '#8b5cf6', '#06b6d4']
      }
    }
  ];

  // Export to Excel function
  const handleExport = () => {
    const exportData = employeeData.map(employee => ({
      "Nhân viên": employee.name,
      "Phòng ban": employee.position,
      "Tổng số dự án": employee.totalProjects,
      "Tổng số nhiệm vụ": employee.totalTasks,
      "Tỉ lệ hoàn thành": `${employee.completionRate}%`,
      "Trạng thái": employee.status
    }));

    // Create CSV content
    const headers = Object.keys(exportData[0]);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'thong-ke-nhan-vien.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const currentStats = statisticsData[selectedDepartment] || statisticsData['Phòng IT'];

  // Mini Pie Chart Component
  const MiniPieChart = ({ data }) => {
    const chartData = {
      labels: data.labels,
      datasets: [
        {
          data: data.data,
          backgroundColor: data.colors,
          borderWidth: 0,
          cutout: '60%',
        }
      ]
    };

    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: false,
        },
      },
      elements: {
        arc: {
          borderWidth: 0,
        }
      }
    };

    return (
      <div style={{ width: '40px', height: '40px' }}>
        <Doughnut data={chartData} options={options} />
      </div>
    );
  };

  // Legend Component
  const StatusLegend = ({ data }) => {
    return (
      <div className="status-legend">
        {data.labels.map((label, index) => (
          <div key={index} className="legend-item">
            <div 
              className="legend-color" 
              style={{ backgroundColor: data.colors[index] }}
            ></div>
            <span className="legend-label">{data.data[index]}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="statistics-placeholder-container">
      {/* Header */}
      <div className="statistics-placeholder-header">
        <div className="statistics-placeholder-title-row">
          <div className="statistics-placeholder-title-with-icon">
            <img src={chartIcon} alt="chart" className="statistics-placeholder-title-icon" />
            <h1 className="statistics-placeholder-list-title">Thống kê công việc</h1>
          </div>
        </div>

        <div className="statistics-placeholder-toolbar-row">
          <div className="statistics-placeholder-toolbar-left">
            <div className={`statistics-placeholder-dropdown ${openDepartmentDropdown ? 'open' : ''}`}>
              <button
                className="statistics-placeholder-dropdown-btn"
                onClick={() => setOpenDepartmentDropdown(!openDepartmentDropdown)}
              >
                <span>{selectedDepartment}</span>
                <img src={dropdownIcon} alt="dropdown" className="statistics-placeholder-dropdown-icon" />
              </button>
              {openDepartmentDropdown && (
                <div className="statistics-placeholder-dropdown-menu">
                  {departments.map(dept => (
                    <div
                      key={dept}
                      className="statistics-placeholder-dropdown-item"
                      onClick={() => {
                        setSelectedDepartment(dept);
                        setOpenDepartmentDropdown(false);
                      }}
                    >
                      {dept}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="statistics-placeholder-cards-wrapper">
        <div className="statistics-placeholder-cards">
          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng nhân sự</span>
              <img src={userGroupIcon} alt="users" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">{currentStats.employees}</div>
            <div className="statistics-placeholder-stat-desc">+2 từ tháng trước</div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng dự án</span>
              <img src={fileIcon} alt="projects" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">{currentStats.projects}</div>
            <div className="statistics-placeholder-stat-desc">+1 từ tháng trước</div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Số lượng công việc</span>
              <img src={chartIcon} alt="tasks" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">{currentStats.tasks}</div>
            <div className="statistics-placeholder-stat-desc">+1 từ tháng trước</div>
          </div>

          <div className="statistics-placeholder-stat-card">
            <div className="statistics-placeholder-stat-title statistics-placeholder-stat-title-flex">
              <span>Tỉ lệ hoàn thành</span>
              <img src={tichIcon} alt="completion" className="statistics-placeholder-stat-icon" />
            </div>
            <div className="statistics-placeholder-stat-value">{currentStats.completionRate}%</div>
            <div className="statistics-placeholder-stat-desc">+5 từ tháng trước</div>
          </div>
        </div>
      </div>

      {/* Chart Section */}
      <div className="statistics-placeholder-chart-section">
        <div className="statistics-placeholder-chart-card">
          <div className="statistics-placeholder-chart-header">
            <div className="statistics-placeholder-chart-title">
              <img src={chartIcon} alt="chart" className="statistics-placeholder-chart-title-icon" />
              <span>Tỉ lệ hoàn thành của phòng ban</span>
            </div>
            <div className="statistics-placeholder-chart-subtitle">
              Phân bố nhiệm vụ theo thời gian cho {selectedDepartment}
            </div>
          </div>

          <div className="statistics-placeholder-chart-controls">
            <div className="statistics-placeholder-chart-type-label">Line</div>
          </div>

          <div className="statistics-placeholder-chart-container">
            <Line data={chartData} options={chartOptions} />
          </div>

          {/* Year Filter Below Chart */}
          <div className="statistics-placeholder-year-filter">
            <div className={`statistics-placeholder-year-dropdown ${openYearDropdown ? 'open' : ''}`}>
              <button
                className="statistics-placeholder-year-dropdown-btn"
                onClick={() => setOpenYearDropdown(!openYearDropdown)}
              >
                <img src={legendNodeIcon} alt="legend" className="statistics-placeholder-legend-icon" />
                <span>{selectedYear}</span>
                <img src={dropdownIcon} alt="dropdown" className="statistics-placeholder-dropdown-icon" />
              </button>
              {openYearDropdown && (
                <div className="statistics-placeholder-year-dropdown-menu">
                  {years.map(year => (
                    <div
                      key={year}
                      className="statistics-placeholder-year-dropdown-item"
                      onClick={() => {
                        setSelectedYear(year);
                        setOpenYearDropdown(false);
                      }}
                    >
                      {year}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Employee Statistics Table */}
      <div className="statistics-placeholder-table-section">
        <div className="statistics-placeholder-table-card">
          <div className="statistics-placeholder-table-header">
            <div className="statistics-placeholder-table-title">
              <img src={userGroupIcon} alt="users" className="statistics-placeholder-table-title-icon" />
              <span>Quản lí nhân viên</span>
            </div>
            <div className="statistics-placeholder-table-subtitle">
              Nhân viên phòng IT
            </div>
          </div>

          {/* Table Filters */}
          <div className="statistical-filters-row">
            <div className="filter-controls">
              <div className="filter-group">
                <input
                  type="text"
                  className="filter-search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Tìm kiếm nhân sự..."
                />
                <img src={searchIcon} alt="search" className="search-icon" />
              </div>

              <div className="filter-group">
                <div className="date-picker-wrapper">
                  <button
                    className="filter-date"
                    onClick={() => setIsDatePickerOpen(!isDatePickerOpen)}
                  >
                    <span>{formatDateRange()}</span>
                    <img src={todayIcon} alt="calendar" className="date-icon" />
                  </button>
                </div>
              </div>

              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsTableProjectOpen(!isTableProjectOpen)}
                  >
                    <span>{selectedTableProject}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isTableProjectOpen && (
                    <div className="procedure-dropdown-menu">
                      {projectOptions.map((project) => (
                        <div
                          key={project}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedTableProject(project);
                            setIsTableProjectOpen(false);
                          }}
                        >
                          {project}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="filter-group">
                <div className="procedure-dropdown">
                  <button
                    className="procedure-dropdown-btn"
                    onClick={() => setIsStatusOpen(!isStatusOpen)}
                  >
                    <span>{selectedStatus}</span>
                    <img src={dropdownIcon} alt="dropdown" className="select-icon" />
                  </button>
                  {isStatusOpen && (
                    <div className="procedure-dropdown-menu">
                      {statusOptions.map((status) => (
                        <div
                          key={status}
                          className="procedure-dropdown-item"
                          onClick={() => {
                            setSelectedStatus(status);
                            setIsStatusOpen(false);
                          }}
                        >
                          {status}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="statistical-actions">
              <button className="btn-export" type="button" onClick={handleExport}>
                <img
                  src={downloadIcon}
                  alt="Xuất Excel"
                  style={{ width: 18, marginRight: 6 }}
                />
                Xuất Excel
              </button>
            </div>
          </div>

          {/* Table */}
          <div className="statistics-placeholder-table-container">
            <table className="statistics-placeholder-table">
              <thead>
                <tr>
                  <th>Nhân viên</th>
                  <th>Tổng số dự án</th>
                  <th>Tổng số nhiệm vụ</th>
                  <th>Tỉ lệ hoàn thành</th>
                  <th>Tổng quan trạng thái</th>
                  <th>Hành động</th>
                </tr>
              </thead>
              <tbody>
                {employeeData.map((employee) => (
                  <tr key={employee.id}>
                    <td>
                      <div className="employee-info">
                        <img
                          src={employee.avatar}
                          alt={employee.name}
                          className="employee-avatar"
                        />
                        <div className="employee-details">
                          <div className="employee-name">{employee.name}</div>
                          <div className="employee-position">{employee.position}</div>
                        </div>
                      </div>
                    </td>
                    <td className="text-center">{employee.totalProjects}</td>
                    <td className="text-center">{employee.totalTasks}</td>
                    <td className="text-center">{employee.completionRate}%</td>
                    <td className="text-center">
                      <div className="status-chart">
                        <MiniPieChart data={employee.statusData} />
                        <StatusLegend data={employee.statusData} />
                      </div>
                    </td>
                    <td className="text-center">
                      <button className="action-btn">
                        <img src={eyeIcon} alt="view" className="action-icon" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatisticsPlaceholder;
