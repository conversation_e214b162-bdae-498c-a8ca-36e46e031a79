import React, { useState, useRef, useEffect } from "react";
import "../../styles/ProjectCreate.css";
import addIcon from "../../assets/add.svg";
import fileTextIcon from "../../assets/file-text.svg";
import DocsIcon from '../../assets/docs.svg';
import PdfIcon from '../../assets/pdf.svg';
import ImageIcon from '../../assets/image.svg';
import startDateIcon from "../../assets/creationdate.svg";
import endDateIcon from "../../assets/enddate.svg";
import closeIcon from "../../assets/closeLoc.svg";
import MemberAddPopup from "./MemberAddPopup";
import { getAllUsers, getUsersByDepartment, transformUserData, updateUser } from "../../api/userManagement";
import { createProject } from "../../api/projectManagement";
import { getAllDepartments, transformDepartmentData } from "../../api/departmentManagement";
import { showSuccess, showInfo, showError } from "../../components/Toastify";
import { ADMIN_ENDPOINTS } from "../../api/endpoints";

const getCurrentUserId = () => {
  try {
    const userRaw = JSON.parse(localStorage.getItem('user') || '{}');
    const user = userRaw.user || userRaw;
    return user._id || user.id || null;
  } catch {
    return null;
  }
};

// Helper functions for file handling
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileIcon = (fileName) => {
  const ext = (fileName || '').split('.').pop().toLowerCase();
  if (ext === 'pdf') return PdfIcon;
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(ext)) return ImageIcon;
  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'].includes(ext)) return DocsIcon;
  return DocsIcon; // Mặc định cho các loại khác
};

// Custom Select Dropdown Component
const CustomSelect = ({ options, value, onChange, placeholder, disabled, error }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const selectedOption = options.find(option => option.value === value);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (option) => {
    onChange(option.value);
    setIsOpen(false);
  };

  return (
    <div className="custom-select" ref={dropdownRef}>
      <div 
        className={`project-form-group select ${error ? 'error' : ''}`} 
        onClick={toggleDropdown}
        style={{ 
          position: 'relative',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.7 : 1
        }}
      >
        <div className="select-selected" style={{
          padding: '6px 8px',
          border: '1px solid #e0e0e0',
          borderRadius: '6px',
          fontSize: '12px',
          backgroundColor: '#fff',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '32px',
          boxSizing: 'border-box'
        }}>
          <span style={{ color: selectedOption ? '#5B5B5B' : '#999' }}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <svg 
            width="12" 
            height="12" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
            style={{ marginLeft: '8px' }}
          >
            <path d="M6 9L12 15L18 9" stroke="#666666" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      
      {isOpen && (
        <div className="project-dropdown">
          {options.map((option) => (
            <div
              key={option.value}
              className="project-dropdown-item"
              onClick={() => handleSelect(option)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const ProjectCreate = ({ onCancel, onCreate }) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    departmentId: "",
    leaderId: "",
    priority: "medium", // Đổi mặc định thành 'medium'
    members: [],
    followers: [],
    attachments: []
  });
  const [departments, setDepartments] = useState([]);
  const [showMemberPopup, setShowMemberPopup] = useState(false);
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingDepartments, setLoadingDepartments] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [fieldErrors, setFieldErrors] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    departmentId: "",
    leaderId: "",
    members: ""
  });
  const fileInputRef = useRef(null);

  // Function để nâng role của user lên leader
  const handlePromoteToLeader = async (userId) => {
    try {
      const selectedUser = users.find(user => user.id === userId);
      if (!selectedUser) return;
      
      const currentRole = (selectedUser.role || "").toLowerCase();
      
      // Chỉ nâng role nếu user hiện tại chưa phải leader trở lên
      const roleHierarchy = ['staff', 'leader', 'departmentHead', 'hr', 'admin'];
      const currentRoleIndex = roleHierarchy.indexOf(currentRole);
      const leaderIndex = roleHierarchy.indexOf('leader');
      
      if (currentRoleIndex !== -1 && currentRoleIndex < leaderIndex) {
        // Promoting user to leader
        await updateUser(userId, { role: 'leader' });
        // Promote thành công nhưng không hiển thị thông báo
      }
    } catch (error) {
      console.error('Error promoting user to leader:', error);
      // Không throw error để không làm gián đoạn việc tạo project thành công
      const userName = selectedUser?.name || 'user';
      showError(`Không thể nâng quyền cho ${userName}. Project đã được tạo thành công.`);
    }
  };

  // Fetch users theo department được chọn
  const fetchUsersByDepartment = async (departmentId) => {
    if (!departmentId) {
      setUsers([]);
      return;
    }
    
    try {
      setLoadingUsers(true);
      const response = await getUsersByDepartment(departmentId, {
        populate: 'department,departmentId',
        include: 'department'
      });
      if (response.success) {
        const transformedUsers = (response.data || []).map(backendUser =>
          transformUserData(backendUser)
        );
        setUsers(transformedUsers);
      }
    } catch (error) {
      console.error('Error fetching users by department:', error);
      setUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Load users khi department thay đổi
  useEffect(() => {
    if (formData.departmentId) {
      fetchUsersByDepartment(formData.departmentId);
      // Reset members và leader khi đổi department
      setFormData(prev => ({
        ...prev,
        members: [],
        leaderId: ""
      }));
    } else {
      setUsers([]);
    }
  }, [formData.departmentId]);

  // Fetch departments from API when component mounts
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setLoadingDepartments(true);
        const response = await getAllDepartments();
        if (response.success) {
          const transformedDepartments = (response.data || []).map(transformDepartmentData);
          setDepartments(transformedDepartments);
        }
      } catch (error) {
        console.error('Error fetching departments:', error);
      } finally {
        setLoadingDepartments(false);
      }
    };
    fetchDepartments();
  }, []);

  // Thêm chọn followers (nâng cao, có thể bỏ qua nếu chưa cần)
  // Khi chọn leader, department, followers: chỉ lưu _id (string)
  const handleInputChange = (field, value) => {
    // Nếu là followers (multi-select), value là mảng _id string
    if (field === 'followers') {
      setFormData(prev => ({ ...prev, followers: value }));
    } else if (field === 'departmentId' || field === 'leaderId') {
      setFormData(prev => ({ ...prev, [field]: value }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    if (error) setError("");
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  // Khi thêm thành viên: chỉ lưu {id, name, email, avatar}
  const handleMemberAdded = (member) => {
    setFormData(prev => ({
      ...prev,
      members: [...prev.members, {
        id: member.id,
        name: member.name,
        email: member.email,
        avatar: member.avatar
      }]
    }));
  };

  // Khi gửi lên API: followers là mảng string _id, members là mảng {userId: id}
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFieldErrors({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      departmentId: "",
      leaderId: "",
      members: ""
    });
    let hasErrors = false;
    const newFieldErrors = {};
    if (!formData.name.trim()) {
      newFieldErrors.name = "Tên dự án không được để trống";
      hasErrors = true;
    }
    // if (!formData.description.trim()) {
    //   newFieldErrors.description = "Vui lòng nhập mô tả dự án";
    //   hasErrors = true;
    // }
    const today = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD
    
    if (!formData.startDate) {
      newFieldErrors.startDate = "Vui lòng chọn thời gian bắt đầu";
      hasErrors = true;
    } else if (formData.startDate < today) {
      newFieldErrors.startDate = "Thời gian bắt đầu không thể là quá khứ";
      hasErrors = true;
    }
    if (!formData.endDate) {
      newFieldErrors.endDate = "Vui lòng chọn thời gian kết thúc";
      hasErrors = true;
    } else if (formData.endDate < today) {
      newFieldErrors.endDate = "Thời gian kết thúc không thể là quá khứ";
      hasErrors = true;
    }
    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {
      newFieldErrors.endDate = "Thời gian kết thúc phải sau thời gian bắt đầu";
      hasErrors = true;
    }
    if (!formData.departmentId) {
      newFieldErrors.departmentId = "Vui lòng chọn phòng ban";
      hasErrors = true;
    }
    if (!formData.leaderId) {
      newFieldErrors.leaderId = "Vui lòng chọn trưởng dự án";
      hasErrors = true;
    }
    if (!formData.members || formData.members.length === 0) {
      newFieldErrors.members = "Vui lòng chọn ít nhất 1 thành viên thực hiện";
      hasErrors = true;
    }
    if (hasErrors) {
      setFieldErrors(newFieldErrors);
      return;
    }
    // Kiểm tra priority hợp lệ
    const allowedPriorities = ["low", "medium", "high", "critical"];
    let priority = formData.priority;
    if (!allowedPriorities.includes(priority)) priority = "medium";
    // Lấy userId hiện tại
    const createdById = getCurrentUserId();
    if (!createdById) {
      setError("Không xác định được người tạo dự án (createdById). Vui lòng đăng nhập lại.");
      return;
    }
    try {
      setLoading(true);
      setError("");
      
      // Tạo FormData để gửi cả dữ liệu và files
      const formDataToSend = new FormData();
      
      // Thêm text data
      formDataToSend.append('name', formData.name.trim());
      formDataToSend.append('description', formData.description.trim());
      formDataToSend.append('startDate', formData.startDate);
      formDataToSend.append('endDate', formData.endDate);
      formDataToSend.append('departmentId', formData.departmentId);
      formDataToSend.append('leaderId', formData.leaderId);
      formDataToSend.append('priority', priority);
      formDataToSend.append('status', 'waiting');
      formDataToSend.append('createdById', createdById);
      
      // Thêm members và followers dưới dạng JSON string
      formDataToSend.append('members', JSON.stringify(formData.members.map(member => ({ userId: member.id }))));
      formDataToSend.append('followers', JSON.stringify(formData.followers.filter(Boolean)));
      
      // Thêm files nếu có
      formData.attachments.forEach(file => {
        formDataToSend.append('files', file);
      });

      console.log('Sending FormData with:', {
        name: formData.name.trim(),
        description: formData.description.trim(),
        filesCount: formData.attachments.length
      });

      // Gửi request đến API mới với FormData
      const token = localStorage.getItem('token');
      const response = await fetch(ADMIN_ENDPOINTS.CREATE_PROJECT_WITH_FILES, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formDataToSend,
        signal: AbortSignal.timeout(60000), // Timeout 60 giây cho file uploads
      });

      const result = await response.json();
      if (response.ok && result.success) {
        // Nâng role của user được chọn làm leader SAU khi tạo project thành công
        if (formData.leaderId) {
          await handlePromoteToLeader(formData.leaderId);
        }
        
        showSuccess(formData.attachments.length > 0 ? 'Tạo dự án với files thành công!' : 'Tạo dự án thành công!');
        
        if (onCreate) {
          onCreate(result.data);
        }
        onCancel();
      } else {
        const errorMessage = result?.message || 'Tạo dự án thất bại!';
        setError(errorMessage);
        console.error('Create project failed:', response.status, response.statusText, result);
      }
    } catch (error) {
      console.error('Error creating project:', error);
      if (error.name === 'TimeoutError') {
        setError('Timeout - file quá lớn hoặc kết nối chậm!');
      } else if (error.name === 'AbortError') {
        setError('Tạo dự án bị hủy!');
      } else {
        setError(error.message || "Có lỗi xảy ra khi tạo dự án");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleAddMember = () => {
    setShowMemberPopup(true);
  };

  const handleRemoveMember = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      members: prev.members.filter((_, index) => index !== indexToRemove)
    }));
  };

  const handleAddFile = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;
    
    // Validate file size (max 20MB per file)
    const validFiles = files.filter(file => {
      if (file.size > 20 * 1024 * 1024) {
        showError(`File ${file.name} quá lớn (tối đa 20MB)`);
        return false;
      }
      return true;
    });

    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...validFiles]
      }));
      showSuccess(`Đã thêm ${validFiles.length} tệp`);
    }
    
    // Reset input để có thể chọn lại cùng file
    e.target.value = '';
  };

  const handleRemoveFile = (indexToRemove) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, index) => index !== indexToRemove)
    }));
  };

  // Chuyển đổi danh sách phòng ban thành định dạng options cho CustomSelect
  const departmentOptions = [
    { value: "", label: "Chọn phòng ban" },
    ...departments.map(dept => ({ value: dept.id, label: dept.name }))
  ];

  // Chuyển đổi danh sách users thành định dạng options cho CustomSelect
  // Hiển thị tất cả thành viên trong phòng ban đã chọn (không chỉ leaders)
  const filteredLeaders = formData.departmentId
    ? users.filter(user => {
        const userDeptId = user.departmentId;
        return userDeptId === formData.departmentId;
      })
    : users; // Nếu chưa chọn phòng ban thì hiển thị tất cả
  const leaderOptions = [
    { value: "", label: "Chọn trưởng dự án" },
    ...filteredLeaders.map(user => {
      const currentRole = (user.role || "").toLowerCase();
      const isAlreadyLeader = ['leader', 'departmentHead', 'hr', 'admin'].includes(currentRole);
      return {
        value: user.id, 
        label: `${user.name}${!isAlreadyLeader ? ' (sẽ được làm Leader)' : ''}`
      };
    })
  ];

  useEffect(() => {
    // Nếu leader hiện tại không thuộc phòng ban đã chọn thì reset leaderId
    if (
      formData.leaderId &&
      !filteredLeaders.some(user => user.id === formData.leaderId)
    ) {
      setFormData(prev => ({ ...prev, leaderId: "" }));
    }
    // eslint-disable-next-line
  }, [formData.departmentId, users]);

  // Chuyển đổi các lựa chọn priority thành định dạng options cho CustomSelect
  const priorityOptions = [
    { value: "low", label: "Thấp" },
    { value: "medium", label: "Trung bình" },
    { value: "high", label: "Cao" },
    { value: "critical", label: "Khẩn cấp" },
  ];



  return (
    <div className="project-create-container">
      <div className="project-create-header">
        <h2>Tạo dự án mới</h2>
        <button className="project-close-btn" onClick={onCancel}>
          <img src={closeIcon} alt="Đóng" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="project-create-form" noValidate>
        {/* Error message */}
        {error && (
          <div className="project-error-message" style={{
            color: '#dc3545',
            backgroundColor: '#f8d7da',
            border: '1px solid #f5c6cb',
            borderRadius: '4px',
            padding: '8px 12px',
            marginBottom: '16px',
            fontSize: '14px',
            whiteSpace: 'pre-wrap'
          }}>
            {error}
          </div>
        )}

        <div className="project-form-group">
          <label>Tên dự án <span className="required-asterisk">*</span></label>
          <input
            type="text"
            placeholder="Hệ thống quản lý bán hàng"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={fieldErrors.name ? 'error' : ''}
          />
          <div className="field-error">{fieldErrors.name}</div>
        </div>

        <div className="project-form-group">
          <label>Mô tả</label>
          <textarea
            placeholder="Phát triển hệ thống quản lý khách hàng"
            value={formData.description}
            onChange={(e) => {
              if (e.target.value.length <= 1000) {
                handleInputChange('description', e.target.value);
              }
            }}
            rows={3}
            maxLength={1000}
            className={fieldErrors.description ? 'error' : ''}
          />
          <div style={{ fontSize: '12px', color: '#888', textAlign: 'right' }}>
            {formData.description.length}/1000 ký tự
          </div>
          <div className="field-error">{fieldErrors.description}</div>
        </div>

        <div className="project-form-row">
          <div className="project-form-group">
            <label>Thời gian bắt đầu <span className="required-asterisk">*</span></label>
            <div className={`project-date-input-group ${fieldErrors.startDate ? 'error' : ''}`} onClick={() => document.querySelector('input[name="projectStartDate"]').showPicker?.()}>
              <img src={startDateIcon} alt="calendar" className="project-calendar-icon" />
              <input
                name="projectStartDate"
                type="date"
                value={formData.startDate}
                min={new Date().toISOString().split('T')[0]}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                placeholder="Chọn ngày bắt đầu"
              />
            </div>
            <div className="field-error">{fieldErrors.startDate}</div>
          </div>
          <div className="project-form-group">
            <label>Thời gian kết thúc <span className="required-asterisk">*</span></label>
            <div className={`project-date-input-group ${fieldErrors.endDate ? 'error' : ''}`} onClick={() => document.querySelector('input[name="projectEndDate"]').showPicker?.()}>
              <img src={endDateIcon} alt="calendar" className="project-calendar-icon" />
              <input
                name="projectEndDate"
                type="date"
                value={formData.endDate}
                min={new Date().toISOString().split('T')[0]}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
                placeholder="Chọn ngày kết thúc"
              />
            </div>
            <div className="field-error">{fieldErrors.endDate}</div>
          </div>
        </div>

        <div className="project-form-row project-three-columns">
          <div className="project-form-group">
            <label>Phòng ban <span className="required-asterisk">*</span></label>
            <CustomSelect
              options={departmentOptions}
              value={formData.departmentId}
              onChange={(value) => handleInputChange('departmentId', value)}
              placeholder="Chọn phòng ban"
              disabled={loadingDepartments}
              error={fieldErrors.departmentId}
            />
            <div className="field-error">{fieldErrors.departmentId}</div>
          </div>
          <div className="project-form-group">
            <label>Trưởng dự án <span className="required-asterisk">*</span></label>
            <CustomSelect
              options={leaderOptions}
              value={formData.leaderId}
              onChange={(value) => handleInputChange('leaderId', value)}
              placeholder="Chọn trưởng dự án"
              disabled={loadingUsers}
              error={fieldErrors.leaderId}
            />
            <div className="field-error">{fieldErrors.leaderId}</div>
          </div>
          <div className="project-form-group">
            <label>Mức độ ưu tiên</label>
            <CustomSelect
              options={priorityOptions}
              value={formData.priority}
              onChange={(value) => handleInputChange('priority', value)}
              placeholder="Chọn mức độ ưu tiên"
            />
          </div>
        </div>

        <div className="project-members-files-row">
          <div className="project-form-group">
            <label >Thành viên thực hiện <span className="required-asterisk">*</span></label>
            <div className="project-members-section">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
                <button type="button" className="project-add-member-btn" onClick={handleAddMember} style={{ width: '24px', height: '24px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 0, border: '1px solid #fff', background: '#fff' }}>
                  <img src={addIcon} alt="Add member" style={{ width: '16px', height: '16px' }} />
                </button>
                {formData.members.map((member, index) => (
                  <div key={index} className="project-members-avatar" title={`${member.name} - ${member.email}`} style={{ width: '24px', height: '24px', borderRadius: '50%', overflow: 'visible', position: 'relative', marginRight: '6px' }}>
                    <img src={member.avatar} alt={member.name} style={{ width: '100%', height: '100%', borderRadius: '50%', objectFit: 'cover' }} />
                    <button
                      type="button"
                      className="project-remove-member-btn"
                      onClick={() => handleRemoveMember(index)}
                      title="Xóa thành viên"
                      style={{ position: 'absolute', top: '-5px', right: '-5px', width: '14px', height: '14px', borderRadius: '50%', border: 'none', background: '#ff4757', color: 'white', fontSize: '9px', fontWeight: 'bold', display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 10 }}
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="field-error">{fieldErrors.members}</div>
            </div>
          </div>

          <div className="project-form-group">
            <label>Tệp đính kèm</label>
            <div className="project-file-items">
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <button type="button" className="project-add-file-btn" onClick={handleAddFile} style={{ width: '24px', height: '24px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: 0, border: '1px solid #fff', background: '#fff' }}>
                  <img src={addIcon} alt="Add file" style={{ width: '16px', height: '16px' }} />
                </button>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileUpload}
                  style={{ display: 'none' }}
                  id="file-upload"
                  ref={fileInputRef}
                />
              </div>
              {formData.attachments.length > 0 && (
                <div className="project-file-list" style={{width: '100%', marginTop: '8px'}}>
                  {formData.attachments.map((file, index) => (
                    <div key={index} className="project-file-item" style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px',
                      border: '1px solid #e0e0e0',
                      borderRadius: '4px',
                      marginBottom: '4px',
                      gap: '8px',
                      backgroundColor: '#f9f9f9'
                    }}>
                      <img src={getFileIcon(file.name)} alt="file" className="project-file-icon" style={{width: '16px', height: '16px'}} />
                      <div className="project-file-info" style={{flex: 1, minWidth: 0}}>
                        <div className="project-file-name" style={{
                          fontSize: '12px',
                          fontWeight: '500',
                          color: '#333',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }} title={file.name}>{file.name}</div>
                        <div className="project-file-size" style={{
                          fontSize: '10px',
                          color: '#666'
                        }}>{formatFileSize(file.size)}</div>
                      </div>
                      <button
                        type="button"
                        className="project-remove-file-btn"
                        onClick={() => handleRemoveFile(index)}
                        title="Xóa file"
                        style={{
                          width: '20px',
                          height: '20px',
                          borderRadius: '50%',
                          border: 'none',
                          background: '#ff4757',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="project-form-action">
          <button 
            type="submit" 
            className="project-create-btn"
            disabled={loading}
            style={{
              opacity: loading ? 0.7 : 1,
              cursor: loading ? 'not-allowed' : 'pointer'
            }}
          >
            {loading ? 'Đang tạo...' : 'Tạo dự án'}
          </button>
        </div>
      </form>

      {/* Popup thêm thành viên */}
              <MemberAddPopup
          isOpen={showMemberPopup}
          onClose={() => setShowMemberPopup(false)}
          onAddMember={handleMemberAdded}
          existingMembers={formData.members}
          users={users}
          loadingUsers={loadingUsers}
          departmentId={formData.departmentId}
          leaderId={formData.leaderId}
          filterByDepartment={true}
        />
    </div>
  );
};

export default ProjectCreate;